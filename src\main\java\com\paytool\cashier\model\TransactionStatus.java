package com.paytool.cashier.model;

/**
 * 交易状态枚举
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public enum TransactionStatus {
    
    /** 待支付 */
    PENDING("待支付", "等待扫描付款码"),
    
    /** 支付中 */
    PROCESSING("支付中", "正在与支付平台进行通信"),
    
    /** 支付成功 */
    SUCCESS("支付成功", "交易完成"),
    
    /** 支付失败 */
    FAILED("支付失败", "交易失败"),
    
    /** 已取消 */
    CANCELLED("已取消", "交易已取消");
    
    private final String displayName;
    private final String description;
    
    TransactionStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 判断是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED || this == CANCELLED;
    }
    
    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    /**
     * 判断是否为失败状态
     */
    public boolean isFailure() {
        return this == FAILED;
    }
}
