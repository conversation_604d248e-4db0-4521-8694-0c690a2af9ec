package com.paytool.cashier.gui;

import com.paytool.cashier.gui.panel.AmountInputPanel;
import com.paytool.cashier.gui.panel.StatusOperationPanel;
import com.paytool.cashier.gui.panel.TransactionInfoPanel;
import com.paytool.cashier.model.Transaction;
import com.paytool.cashier.model.TransactionStatus;
import com.paytool.cashier.service.PaymentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;

/**
 * 主界面窗口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class MainFrame extends JFrame {
    
    private static final Logger logger = LoggerFactory.getLogger(MainFrame.class);
    
    private AmountInputPanel amountInputPanel;
    private TransactionInfoPanel transactionInfoPanel;
    private StatusOperationPanel statusOperationPanel;
    
    private PaymentService paymentService;
    private Transaction currentTransaction;
    
    public MainFrame() {
        this.paymentService = PaymentService.getInstance();
        initComponents();
        setupLayout();
        setupEventHandlers();
        setupWindow();
    }
    
    /**
     * 初始化组件
     */
    private void initComponents() {
        amountInputPanel = new AmountInputPanel();
        transactionInfoPanel = new TransactionInfoPanel();
        statusOperationPanel = new StatusOperationPanel();
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout(10, 10));
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // 左侧面板 - 金额输入区
        mainPanel.add(amountInputPanel, BorderLayout.WEST);
        
        // 中间面板 - 交易信息区
        mainPanel.add(transactionInfoPanel, BorderLayout.CENTER);
        
        // 右侧面板 - 状态操作区
        mainPanel.add(statusOperationPanel, BorderLayout.EAST);
        
        add(mainPanel, BorderLayout.CENTER);
        
        // 底部状态栏
        JPanel statusBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusBar.setBorder(BorderFactory.createLoweredBevelBorder());
        JLabel statusLabel = new JLabel("就绪");
        statusBar.add(statusLabel);
        add(statusBar, BorderLayout.SOUTH);
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 金额输入面板事件
        amountInputPanel.setAmountChangeListener(this::onAmountChanged);
        
        // 状态操作面板事件
        statusOperationPanel.setScanListener(this::onScanClicked);
        statusOperationPanel.setClearListener(this::onClearClicked);
        statusOperationPanel.setQueryListener(this::onQueryClicked);
        statusOperationPanel.setPaymentCodeListener(this::onPaymentCodeEntered);
        
        // 窗口关闭事件
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                onWindowClosing();
            }
        });
    }
    
    /**
     * 设置窗口属性
     */
    private void setupWindow() {
        setTitle("扫码支付收银工具 v1.0.0");
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        setSize(1000, 600);
        setLocationRelativeTo(null);
        setResizable(false);
        
        // 设置图标
        try {
            setIconImage(Toolkit.getDefaultToolkit().getImage(
                getClass().getResource("/icon.png")));
        } catch (Exception e) {
            logger.debug("未找到应用图标");
        }
    }
    
    /**
     * 金额变化事件
     */
    private void onAmountChanged(BigDecimal amount) {
        logger.debug("金额变化: {}", amount);
        updateTransactionInfo();
    }
    
    /**
     * 扫码按钮点击事件
     */
    private void onScanClicked() {
        logger.info("点击扫码按钮");
        
        BigDecimal amount = amountInputPanel.getAmount();
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            showErrorMessage("请先输入支付金额");
            return;
        }
        
        // 聚焦到支付码输入框
        statusOperationPanel.focusPaymentCodeInput();
        showInfoMessage("请扫描或输入支付码");
    }
    
    /**
     * 清空按钮点击事件
     */
    private void onClearClicked() {
        logger.info("点击清空按钮");
        clearAll();
    }
    
    /**
     * 查询按钮点击事件
     */
    private void onQueryClicked() {
        logger.info("点击查询按钮");
        
        String orderNo = JOptionPane.showInputDialog(this, "请输入订单号:", "查询交易", JOptionPane.QUESTION_MESSAGE);
        if (orderNo != null && !orderNo.trim().isEmpty()) {
            queryTransaction(orderNo.trim());
        }
    }
    
    /**
     * 支付码输入事件
     */
    private void onPaymentCodeEntered(String paymentCode) {
        logger.info("输入支付码: {}", paymentCode);
        
        BigDecimal amount = amountInputPanel.getAmount();
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            showErrorMessage("请先输入支付金额");
            statusOperationPanel.clearPaymentCode();
            return;
        }
        
        if (!paymentService.isValidPaymentCode(paymentCode)) {
            showErrorMessage("支付码格式不正确");
            statusOperationPanel.clearPaymentCode();
            return;
        }
        
        processPayment(amount, paymentCode);
    }
    
    /**
     * 处理支付
     */
    private void processPayment(BigDecimal amount, String paymentCode) {
        try {
            // 创建交易
            currentTransaction = paymentService.createTransaction(amount, paymentCode);
            updateTransactionInfo();
            updateStatus();
            
            // 异步处理支付
            CompletableFuture<Transaction> future = paymentService.processPayment(currentTransaction);
            future.thenAccept(transaction -> {
                SwingUtilities.invokeLater(() -> {
                    currentTransaction = transaction;
                    updateTransactionInfo();
                    updateStatus();
                    
                    if (transaction.getStatus().isSuccess()) {
                        showSuccessMessage("支付成功！");
                        playSuccessSound();
                    } else if (transaction.getStatus().isFailure()) {
                        showErrorMessage("支付失败: " + transaction.getFailureReason());
                        playFailureSound();
                    }
                });
            });
            
        } catch (Exception e) {
            logger.error("处理支付异常", e);
            showErrorMessage("处理支付失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询交易
     */
    private void queryTransaction(String orderNo) {
        Transaction transaction = paymentService.queryTransaction(orderNo);
        if (transaction != null) {
            currentTransaction = transaction;
            amountInputPanel.setAmount(transaction.getAmount());
            statusOperationPanel.setPaymentCode(transaction.getPaymentCode());
            updateTransactionInfo();
            updateStatus();
            showInfoMessage("查询成功");
        } else {
            showErrorMessage("未找到订单: " + orderNo);
        }
    }
    
    /**
     * 更新交易信息显示
     */
    private void updateTransactionInfo() {
        if (currentTransaction != null) {
            transactionInfoPanel.updateTransaction(currentTransaction);
        } else {
            transactionInfoPanel.clear();
        }
    }
    
    /**
     * 更新状态显示
     */
    private void updateStatus() {
        if (currentTransaction != null) {
            statusOperationPanel.updateStatus(currentTransaction.getStatus());
        } else {
            statusOperationPanel.updateStatus(TransactionStatus.PENDING);
        }
    }
    
    /**
     * 清空所有输入
     */
    private void clearAll() {
        currentTransaction = null;
        amountInputPanel.clear();
        transactionInfoPanel.clear();
        statusOperationPanel.clear();
    }
    
    /**
     * 显示成功消息
     */
    private void showSuccessMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "成功", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * 显示错误消息
     */
    private void showErrorMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "错误", JOptionPane.ERROR_MESSAGE);
    }
    
    /**
     * 显示信息消息
     */
    private void showInfoMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "提示", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * 播放成功音效
     */
    private void playSuccessSound() {
        Toolkit.getDefaultToolkit().beep();
    }
    
    /**
     * 播放失败音效
     */
    private void playFailureSound() {
        Toolkit.getDefaultToolkit().beep();
    }
    
    /**
     * 窗口关闭事件
     */
    private void onWindowClosing() {
        int option = JOptionPane.showConfirmDialog(this,
            "确定要退出收银工具吗？",
            "确认退出",
            JOptionPane.YES_NO_OPTION);

        if (option == JOptionPane.YES_OPTION) {
            logger.info("用户退出应用程序");

            // 清理资源
            if (statusOperationPanel != null) {
                statusOperationPanel.dispose();
            }

            System.exit(0);
        }
    }
}
