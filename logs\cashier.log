2025-09-16 10:41:21.226 [main] INFO  c.paytool.cashier.CashierApplication - 启动扫码支付收银工具...
2025-09-16 10:41:21.857 [main] INFO  c.p.cashier.service.DatabaseService - 创建数据目录: D:\PyCharm_WS\PayTool\data
2025-09-16 10:41:23.246 [main] INFO  c.p.cashier.service.DatabaseService - 数据库表创建完成
2025-09-16 10:41:23.248 [main] INFO  c.p.cashier.service.DatabaseService - 数据库初始化完成: data\cashier.db
2025-09-16 10:41:24.304 [AWT-EventQueue-0] INFO  c.p.cashier.service.ScannerService - 注册扫码监听器到组件: JTextField
2025-09-16 10:41:24.319 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 未找到应用图标
2025-09-16 10:41:24.626 [AWT-EventQueue-0] INFO  c.paytool.cashier.CashierApplication - 收银工具界面启动成功
2025-09-16 10:41:28.757 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5
2025-09-16 10:41:29.188 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 59
2025-09-16 10:41:29.463 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 596
2025-09-16 10:41:29.680 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5964
2025-09-16 10:41:29.868 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 59648
2025-09-16 10:41:32.453 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 59648
2025-09-16 10:41:32.766 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 59648.8
2025-09-16 10:41:33.205 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 59648.85
2025-09-16 10:41:43.513 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击扫码按钮
2025-09-16 10:41:52.437 [AWT-EventQueue-0] DEBUG c.p.cashier.service.ScannerService - 开始扫码输入
2025-09-16 10:41:52.554 [ScannerService] DEBUG c.p.cashier.service.ScannerService - 扫码数据无效: ***
2025-09-16 10:41:52.569 [AWT-EventQueue-0] DEBUG c.p.cashier.service.ScannerService - 开始扫码输入
2025-09-16 10:41:52.690 [ScannerService] DEBUG c.p.cashier.service.ScannerService - 扫码数据无效: ***
2025-09-16 10:41:52.705 [AWT-EventQueue-0] DEBUG c.p.cashier.service.ScannerService - 开始扫码输入
2025-09-16 10:41:52.815 [ScannerService] DEBUG c.p.cashier.service.ScannerService - 扫码数据无效: ***
2025-09-16 10:41:52.846 [AWT-EventQueue-0] DEBUG c.p.cashier.service.ScannerService - 开始扫码输入
2025-09-16 10:41:53.171 [ScannerService] DEBUG c.p.cashier.service.ScannerService - 扫码数据无效: ***
2025-09-16 10:42:01.502 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击扫码按钮
2025-09-16 10:42:07.223 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 59648.8
2025-09-16 10:42:08.474 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 59648
2025-09-16 10:42:24.297 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击扫码按钮
2025-09-16 10:42:29.921 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击查询按钮
2025-09-16 10:43:01.055 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 324234224354542113
2025-09-16 10:43:01.071 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161043010001
2025-09-16 10:43:01.289 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161043010001
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 10:43:01.321 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 40 common frames omitted
2025-09-16 10:43:03.747 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 324234224354542113
2025-09-16 10:43:03.747 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161043030002
2025-09-16 10:43:03.950 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161043030002
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 10:43:03.950 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 43 common frames omitted
2025-09-16 10:43:30.281 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击清空按钮
2025-09-16 10:43:30.281 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 0
2025-09-16 10:43:45.931 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击查询按钮
2025-09-16 10:43:50.900 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击扫码按钮
2025-09-16 10:43:53.441 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 20
2025-09-16 10:43:55.118 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 200
2025-09-16 10:43:56.753 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击扫码按钮
2025-09-16 10:43:59.893 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 21313
2025-09-16 10:44:05.131 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击扫码按钮
2025-09-16 10:44:08.368 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 231435346463
2025-09-16 10:44:08.368 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161044080003
2025-09-16 10:44:08.555 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161044080003
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 10:44:08.555 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 40 common frames omitted
2025-09-16 10:44:12.643 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 231435346463
2025-09-16 10:44:12.643 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161044120004
2025-09-16 10:44:12.824 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161044120004
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 10:44:12.824 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 43 common frames omitted
2025-09-16 10:45:16.081 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击扫码按钮
2025-09-16 10:45:22.461 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击清空按钮
2025-09-16 10:45:22.461 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 0
2025-09-16 10:45:24.004 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5
2025-09-16 10:45:24.301 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 58
2025-09-16 10:45:25.101 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 20
2025-09-16 10:45:26.755 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 50
2025-09-16 10:45:32.309 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击扫码按钮
2025-09-16 10:45:45.035 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: afafafa
2025-09-16 10:46:38.157 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 20
2025-09-16 10:46:40.254 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 20.0
2025-09-16 10:46:40.664 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 20
2025-09-16 10:46:40.850 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 20
2025-09-16 10:46:41.038 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 2
2025-09-16 10:46:41.257 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 0
2025-09-16 10:46:41.638 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5
2025-09-16 10:46:41.942 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 55
2025-09-16 10:46:42.146 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 557
2025-09-16 10:46:42.349 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5578
2025-09-16 10:46:42.584 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 55786
2025-09-16 10:46:43.904 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5578
2025-09-16 10:46:45.450 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5578
2025-09-16 10:46:45.890 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5578.5
2025-09-16 10:46:46.549 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5578.56
2025-09-16 10:46:54.348 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5578.5
2025-09-16 10:46:54.536 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5578
2025-09-16 10:46:54.708 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5578
2025-09-16 10:46:54.881 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 557
2025-09-16 10:46:55.069 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 55
2025-09-16 10:46:55.256 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5
2025-09-16 10:46:55.443 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 0
2025-09-16 10:46:55.622 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 0
2025-09-16 10:47:16.000 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5
2025-09-16 10:47:16.249 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 52
2025-09-16 10:47:16.561 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 524
2025-09-16 10:47:16.969 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5246
2025-09-16 10:47:17.969 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5246
2025-09-16 10:47:18.360 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5246.2
2025-09-16 10:47:19.846 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5246.22
2025-09-16 10:47:22.788 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击扫码按钮
2025-09-16 10:47:25.827 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 132423523a32424
2025-09-16 10:47:25.827 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161047250005
2025-09-16 10:47:25.997 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161047250005
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 10:47:25.997 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 40 common frames omitted
2025-09-16 10:47:31.983 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 132423523a32424
2025-09-16 10:47:31.983 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161047310006
2025-09-16 10:47:32.295 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161047310006
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 10:47:32.295 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 43 common frames omitted
2025-09-16 10:49:22.295 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 132423523a32424
2025-09-16 10:49:22.295 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161049220007
2025-09-16 10:49:22.437 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161049220007
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 10:49:22.437 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 40 common frames omitted
2025-09-16 10:49:23.967 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 132423523a32424
2025-09-16 10:49:23.967 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161049230008
2025-09-16 10:49:24.131 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161049230008
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 10:49:24.131 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 43 common frames omitted
2025-09-16 10:52:06.940 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5246.2
2025-09-16 10:52:07.580 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 5246
2025-09-16 10:52:12.193 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 132423523a324241
2025-09-16 10:52:12.193 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161052120009
2025-09-16 10:52:12.365 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161052120009
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 10:52:12.365 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 40 common frames omitted
2025-09-16 10:52:14.083 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: 132423523a324241
2025-09-16 10:52:14.083 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161052140010
2025-09-16 10:52:14.239 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161052140010
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 10:52:14.239 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 43 common frames omitted
2025-09-16 11:00:34.979 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 用户退出应用程序
2025-09-16 11:00:34.979 [AWT-EventQueue-0] INFO  c.p.cashier.service.ScannerService - 取消扫码监听器
2025-09-16 11:08:41.136 [main] INFO  c.paytool.cashier.CashierApplication - 启动扫码支付收银工具...
2025-09-16 11:08:41.927 [main] INFO  c.p.cashier.service.DatabaseService - 数据库表创建完成
2025-09-16 11:08:41.927 [main] INFO  c.p.cashier.service.DatabaseService - 数据库初始化完成: data\cashier.db
2025-09-16 11:08:42.377 [AWT-EventQueue-0] INFO  c.p.cashier.service.ScannerService - 注册扫码监听器到组件: JTextField
2025-09-16 11:08:42.377 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 未找到应用图标
2025-09-16 11:08:42.518 [AWT-EventQueue-0] INFO  c.paytool.cashier.CashierApplication - 收银工具界面启动成功
2025-09-16 11:08:46.981 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 点击扫码按钮
2025-09-16 11:08:53.824 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 1
2025-09-16 11:08:54.087 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 11
2025-09-16 11:08:54.306 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 113
2025-09-16 11:08:54.559 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 1134
2025-09-16 11:08:54.779 [AWT-EventQueue-0] DEBUG com.paytool.cashier.gui.MainFrame - 金额变化: 11348
2025-09-16 11:08:56.970 [AWT-EventQueue-0] DEBUG c.p.cashier.service.ScannerService - 开始扫码输入
2025-09-16 11:08:57.110 [ScannerService] DEBUG c.p.cashier.service.ScannerService - 扫码数据无效: ***
2025-09-16 11:08:57.141 [AWT-EventQueue-0] DEBUG c.p.cashier.service.ScannerService - 开始扫码输入
2025-09-16 11:08:57.267 [ScannerService] DEBUG c.p.cashier.service.ScannerService - 扫码数据无效: ***
2025-09-16 11:08:57.309 [AWT-EventQueue-0] DEBUG c.p.cashier.service.ScannerService - 开始扫码输入
2025-09-16 11:08:57.438 [ScannerService] DEBUG c.p.cashier.service.ScannerService - 扫码数据无效: ***
2025-09-16 11:08:57.468 [AWT-EventQueue-0] DEBUG c.p.cashier.service.ScannerService - 开始扫码输入
2025-09-16 11:08:57.578 [ScannerService] DEBUG c.p.cashier.service.ScannerService - 扫码数据无效: ***
2025-09-16 11:09:07.202 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: fafag123131242424
2025-09-16 11:09:07.202 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161109070001
2025-09-16 11:09:07.383 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161109070001
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 11:09:07.398 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel$1.keyPressed(StatusOperationPanel.java:157)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:250)
	at java.awt.AWTEventMulticaster.keyPressed(AWTEventMulticaster.java:249)
	at java.awt.Component.processKeyEvent(Component.java:6491)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2832)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 40 common frames omitted
2025-09-16 11:09:09.994 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 输入支付码: fafag123131242424
2025-09-16 11:09:09.994 [AWT-EventQueue-0] DEBUG c.p.cashier.service.OrderService - 生成订单号: PT202509161109090002
2025-09-16 11:09:10.167 [AWT-EventQueue-0] ERROR c.paytool.cashier.dao.TransactionDao - 保存交易记录失败: PT202509161109090002
java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
2025-09-16 11:09:10.167 [AWT-EventQueue-0] ERROR com.paytool.cashier.gui.MainFrame - 处理支付异常
java.lang.RuntimeException: 保存交易记录失败
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:68)
	at com.paytool.cashier.service.PaymentService.createTransaction(PaymentService.java:55)
	at com.paytool.cashier.gui.MainFrame.processPayment(MainFrame.java:196)
	at com.paytool.cashier.gui.MainFrame.onPaymentCodeEntered(MainFrame.java:187)
	at com.paytool.cashier.gui.panel.StatusOperationPanel.lambda$setupEventHandlers$3(StatusOperationPanel.java:167)
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:508)
	at javax.swing.JTextField.postActionEvent(JTextField.java:721)
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:836)
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1663)
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2882)
	at javax.swing.JComponent.processKeyBindings(JComponent.java:2929)
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2845)
	at java.awt.Component.processEvent(Component.java:6310)
	at java.awt.Container.processEvent(Container.java:2236)
	at java.awt.Component.dispatchEventImpl(Component.java:4889)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1954)
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:806)
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1074)
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:945)
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:771)
	at java.awt.Component.dispatchEventImpl(Component.java:4760)
	at java.awt.Container.dispatchEventImpl(Container.java:2294)
	at java.awt.Window.dispatchEventImpl(Window.java:2746)
	at java.awt.Component.dispatchEvent(Component.java:4711)
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:758)
	at java.awt.EventQueue.access$500(EventQueue.java:97)
	at java.awt.EventQueue$3.run(EventQueue.java:709)
	at java.awt.EventQueue$3.run(EventQueue.java:703)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:90)
	at java.awt.EventQueue$4.run(EventQueue.java:731)
	at java.awt.EventQueue$4.run(EventQueue.java:729)
	at java.security.AccessController.doPrivileged(Native Method)
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:80)
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:728)
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:201)
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:116)
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:105)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:93)
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:82)
Caused by: java.sql.SQLFeatureNotSupportedException: not implemented by SQLite JDBC driver
	at org.sqlite.jdbc3.JDBC3PreparedStatement.unsupported(JDBC3PreparedStatement.java:448)
	at org.sqlite.jdbc3.JDBC3Statement.getGeneratedKeys(JDBC3Statement.java:361)
	at com.paytool.cashier.dao.TransactionDao.save(TransactionDao.java:56)
	... 43 common frames omitted
2025-09-16 11:09:15.671 [AWT-EventQueue-0] INFO  com.paytool.cashier.gui.MainFrame - 用户退出应用程序
2025-09-16 11:09:15.686 [AWT-EventQueue-0] INFO  c.p.cashier.service.ScannerService - 取消扫码监听器
