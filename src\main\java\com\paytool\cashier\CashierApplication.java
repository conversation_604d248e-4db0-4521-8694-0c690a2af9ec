package com.paytool.cashier;

import com.paytool.cashier.gui.MainFrame;
import com.paytool.cashier.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;

/**
 * 扫码支付收银工具主应用程序
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class CashierApplication {

    private static final Logger logger = LoggerFactory.getLogger(CashierApplication.class);

    public static void main(String[] args) {
        logger.info("启动扫码支付收银工具...");

        try {
            // 设置系统外观
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());

            // 初始化数据库
            DatabaseService.getInstance().initDatabase();

            // 启动GUI界面
            SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    try {
                        MainFrame mainFrame = new MainFrame();
                        mainFrame.setVisible(true);
                        logger.info("收银工具界面启动成功");
                    } catch (Exception e) {
                        logger.error("启动GUI界面失败", e);
                        JOptionPane.showMessageDialog(null,
                            "启动应用程序失败: " + e.getMessage(),
                            "错误",
                            JOptionPane.ERROR_MESSAGE);
                        System.exit(1);
                    }
                }
            });

        } catch (Exception e) {
            logger.error("应用程序启动失败", e);
            JOptionPane.showMessageDialog(null,
                "应用程序启动失败: " + e.getMessage(),
                "错误",
                JOptionPane.ERROR_MESSAGE);
            System.exit(1);
        }
    }
}