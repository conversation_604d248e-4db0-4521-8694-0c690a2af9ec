package com.paytool.cashier.service;

import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * 订单服务测试类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class OrderServiceTest {
    
    private OrderService orderService;
    
    @Before
    public void setUp() {
        orderService = OrderService.getInstance();
    }
    
    @Test
    public void testGenerateOrderNo() {
        // 测试生成订单号
        String orderNo1 = orderService.generateOrderNo();
        String orderNo2 = orderService.generateOrderNo();
        
        // 验证订单号不为空
        assertNotNull(orderNo1);
        assertNotNull(orderNo2);
        
        // 验证订单号长度
        assertEquals(22, orderNo1.length());
        assertEquals(22, orderNo2.length());
        
        // 验证订单号前缀
        assertTrue(orderNo1.startsWith("PT"));
        assertTrue(orderNo2.startsWith("PT"));
        
        // 验证订单号唯一性
        assertNotEquals(orderNo1, orderNo2);
        
        System.out.println("生成的订单号1: " + orderNo1);
        System.out.println("生成的订单号2: " + orderNo2);
    }
    
    @Test
    public void testIsValidOrderNo() {
        // 测试有效的订单号
        String validOrderNo = orderService.generateOrderNo();
        assertTrue(orderService.isValidOrderNo(validOrderNo));
        
        // 测试无效的订单号
        assertFalse(orderService.isValidOrderNo(null));
        assertFalse(orderService.isValidOrderNo(""));
        assertFalse(orderService.isValidOrderNo("PT"));
        assertFalse(orderService.isValidOrderNo("PT123"));
        assertFalse(orderService.isValidOrderNo("XX202312251430001234"));
        assertFalse(orderService.isValidOrderNo("PT20231225143000123"));
        assertFalse(orderService.isValidOrderNo("PT2023122514300012345"));
        
        System.out.println("订单号验证测试通过");
    }
    
    @Test
    public void testOrderNoFormat() {
        String orderNo = orderService.generateOrderNo();
        
        // 验证格式: PT + 14位时间戳 + 4位序列号
        assertEquals("PT", orderNo.substring(0, 2));
        
        String timestamp = orderNo.substring(2, 16);
        assertEquals(14, timestamp.length());
        assertTrue(timestamp.matches("\\d{14}"));
        
        String sequence = orderNo.substring(16, 22);
        assertEquals(6, sequence.length());
        assertTrue(sequence.matches("\\d{6}"));
        
        System.out.println("订单号格式验证通过: " + orderNo);
    }
    
    @Test
    public void testSequenceIncrement() {
        // 生成多个订单号，验证序列号递增
        String orderNo1 = orderService.generateOrderNo();
        String orderNo2 = orderService.generateOrderNo();
        String orderNo3 = orderService.generateOrderNo();
        
        // 提取序列号
        int seq1 = Integer.parseInt(orderNo1.substring(16));
        int seq2 = Integer.parseInt(orderNo2.substring(16));
        int seq3 = Integer.parseInt(orderNo3.substring(16));
        
        // 验证序列号递增（考虑到可能跨越时间戳变化）
        assertTrue(seq2 > seq1 || seq2 == 1); // 序列号重置的情况
        assertTrue(seq3 > seq2 || seq3 == 1); // 序列号重置的情况
        
        System.out.println("序列号递增验证通过");
        System.out.println("序列号1: " + seq1);
        System.out.println("序列号2: " + seq2);
        System.out.println("序列号3: " + seq3);
    }
}
