package com.paytool.cashier.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订单服务类
 * 负责生成唯一的订单号
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class OrderService {
    
    private static final Logger logger = LoggerFactory.getLogger(OrderService.class);
    
    private static OrderService instance;
    private final AtomicLong sequence = new AtomicLong(1);
    
    private OrderService() {
    }
    
    public static synchronized OrderService getInstance() {
        if (instance == null) {
            instance = new OrderService();
        }
        return instance;
    }
    
    /**
     * 生成订单号
     * 格式: PT + yyyyMMddHHmmss + 4位序列号
     * 例如: PT202312251430001234
     */
    public String generateOrderNo() {
        LocalDateTime now = LocalDateTime.now();
        String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        long seq = sequence.getAndIncrement();
        
        // 如果序列号超过9999，重置为1
        if (seq > 9999) {
            sequence.set(1);
            seq = 1;
        }
        
        String orderNo = String.format("PT%s%04d", timestamp, seq);
        logger.debug("生成订单号: {}", orderNo);
        
        return orderNo;
    }
    
    /**
     * 验证订单号格式
     */
    public boolean isValidOrderNo(String orderNo) {
        if (orderNo == null || orderNo.length() != 22) {
            return false;
        }
        
        if (!orderNo.startsWith("PT")) {
            return false;
        }
        
        try {
            // 验证时间戳部分
            String timestamp = orderNo.substring(2, 16);
            LocalDateTime.parse(timestamp, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            
            // 验证序列号部分
            String seqStr = orderNo.substring(16);
            Integer.parseInt(seqStr);
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
