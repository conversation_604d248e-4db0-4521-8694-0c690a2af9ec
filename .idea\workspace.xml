<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="34428ff6-f2a0-4609-8718-8b4047f1a1bb" name="Default Changelist" comment="" />
    <ignored path="$PROJECT_DIR$/target/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/gui/panel/StatusOperationPanel.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="-2134">
              <caret line="166" column="37" selection-start-line="166" selection-start-column="37" selection-end-line="166" selection-end-column="37" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/gui/panel/TransactionInfoPanel.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="226">
              <caret line="245" selection-start-line="245" selection-end-line="245" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/service/PaymentService.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="-180">
              <caret line="42" column="81" lean-forward="true" selection-start-line="42" selection-start-column="81" selection-end-line="42" selection-end-column="81" />
              <folding>
                <element signature="imports" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/dao/TransactionDao.java">
          <provider selected="true" editor-type-id="text-editor">
            <state>
              <caret line="35" column="47" lean-forward="true" selection-start-line="35" selection-start-column="47" selection-end-line="35" selection-end-column="47" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/model/Transaction.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="-1678">
              <caret line="11" column="13" selection-start-line="11" selection-start-column="13" selection-end-line="11" selection-end-column="13" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/model/TransactionStatus.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="-598">
              <caret line="10" column="14" selection-start-line="10" selection-start-column="14" selection-end-line="10" selection-end-column="14" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/gui/MainFrame.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="140">
              <caret line="195" column="59" selection-start-line="195" selection-start-column="59" selection-end-line="195" selection-end-column="59" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/test/java/com/paytool/cashier/dao/TransactionDaoTest.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="128">
              <caret line="23" column="4" selection-start-line="23" selection-start-column="4" selection-end-line="23" selection-end-column="4" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/resources/logback.xml">
          <provider selected="true" editor-type-id="text-editor" />
        </entry>
      </file>
    </leaf>
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/src/main/java/com/paytool/cashier/CashierApplication.java" />
        <option value="$PROJECT_DIR$/src/main/java/com/paytool/cashier/dao/TransactionDao.java" />
      </list>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\apache-maven-3.6.0\repository" />
        <option name="mavenHome" value="D:/apache-maven-3.6.0" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.0\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenProjectNavigator">
    <treeState>
      <expand>
        <path>
          <item name="" type="16c1761:MavenProjectsStructure$RootNode" />
          <item name="Scan Pay Cashier Tool" type="9519ce18:MavenProjectsStructure$ProjectNode" />
        </path>
        <path>
          <item name="" type="16c1761:MavenProjectsStructure$RootNode" />
          <item name="Scan Pay Cashier Tool" type="9519ce18:MavenProjectsStructure$ProjectNode" />
          <item name="Lifecycle" type="58874e2:MavenProjectsStructure$LifecycleNode" />
        </path>
      </expand>
      <select />
    </treeState>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="506" />
    <option name="y" value="22" />
    <option name="width" value="875" />
    <option name="height" value="645" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="scan-pay-cashier" type="b2602c69:ProjectViewProjectNode" />
              <item name="PayTool" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="scan-pay-cashier" type="b2602c69:ProjectViewProjectNode" />
              <item name="PayTool" type="462c0819:PsiDirectoryNode" />
              <item name="logs" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="scan-pay-cashier" type="b2602c69:ProjectViewProjectNode" />
              <item name="PayTool" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="scan-pay-cashier" type="b2602c69:ProjectViewProjectNode" />
              <item name="PayTool" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="scan-pay-cashier" type="b2602c69:ProjectViewProjectNode" />
              <item name="PayTool" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="scan-pay-cashier" type="b2602c69:ProjectViewProjectNode" />
              <item name="PayTool" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="cashier" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="scan-pay-cashier" type="b2602c69:ProjectViewProjectNode" />
              <item name="PayTool" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="cashier" type="462c0819:PsiDirectoryNode" />
              <item name="dao" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="PackagesPane" />
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="project.structure.last.edited" value="Project" />
    <property name="project.structure.proportion" value="0.0" />
    <property name="project.structure.side.proportion" value="0.0" />
    <property name="settings.editor.selected.configurable" value="MavenSettings" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="CashierApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.paytool.cashier.CashierApplication" />
      <module name="scan-pay-cashier" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.paytool.cashier.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.CashierApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="34428ff6-f2a0-4609-8718-8b4047f1a1bb" name="Default Changelist" comment="" />
      <created>1757990008179</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1757990008179</updated>
      <workItem from="1757990015597" duration="2502000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="2502000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="1382" height="744" extended-state="6" />
    <layout>
      <window_info id="Designer" />
      <window_info id="UI Designer" />
      <window_info id="Favorites" side_tool="true" />
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.23903178" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Docker" show_stripe_button="false" />
      <window_info anchor="bottom" id="Database Changes" />
      <window_info anchor="bottom" id="Version Control" />
      <window_info anchor="bottom" id="Messages" weight="0.3289689" />
      <window_info anchor="bottom" id="Terminal" />
      <window_info anchor="bottom" id="Event Log" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info active="true" anchor="bottom" id="Run" order="2" visible="true" weight="0.62520456" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="right" id="Palette" />
      <window_info anchor="right" id="Maven" weight="0.16263238" />
      <window_info anchor="right" id="Palette&#9;" />
      <window_info anchor="right" id="Database" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/pom.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="200">
          <caret line="33" lean-forward="true" selection-start-line="33" selection-end-line="33" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/service/DatabaseService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="128">
          <caret line="18" column="13" selection-start-line="18" selection-start-column="13" selection-end-line="18" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/service/OrderService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="128">
          <caret line="16" column="13" selection-start-line="16" selection-start-column="13" selection-end-line="16" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/service/ScannerService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-452">
          <caret line="22" column="4" selection-start-line="22" selection-start-column="4" selection-end-line="22" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="jar://D:/Java/jdk1.8.0_144/src.zip!/javax/swing/UIManager.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="128">
          <caret line="176" column="22" selection-start-line="176" selection-start-column="13" selection-end-line="176" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="jar://D:/Java/jdk1.8.0_144/src.zip!/java/util/function/Consumer.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="132">
          <caret line="48" column="9" selection-start-line="48" selection-start-column="9" selection-end-line="48" selection-end-column="9" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/logs/cashier.log">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="160">
          <caret line="8" column="68" selection-start-line="8" selection-start-column="68" selection-end-line="8" selection-end-column="68" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/CashierApplication.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="880">
          <caret line="49" column="40" selection-start-line="49" selection-start-column="40" selection-end-line="49" selection-end-column="40" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/gui/panel/AmountInputPanel.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="148">
          <caret line="193" column="33" selection-start-line="193" selection-start-column="33" selection-end-line="193" selection-end-column="33" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/gui/panel/StatusOperationPanel.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-2134">
          <caret line="166" column="37" selection-start-line="166" selection-start-column="37" selection-end-line="166" selection-end-column="37" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/gui/panel/TransactionInfoPanel.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="226">
          <caret line="245" selection-start-line="245" selection-end-line="245" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/test/java/com/paytool/cashier/dao/TransactionDaoTest.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="128">
          <caret line="23" column="4" selection-start-line="23" selection-start-column="4" selection-end-line="23" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/resources/logback.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/model/Transaction.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1678">
          <caret line="11" column="13" selection-start-line="11" selection-start-column="13" selection-end-line="11" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/model/TransactionStatus.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-598">
          <caret line="10" column="14" selection-start-line="10" selection-start-column="14" selection-end-line="10" selection-end-column="14" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/gui/MainFrame.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="140">
          <caret line="195" column="59" selection-start-line="195" selection-start-column="59" selection-end-line="195" selection-end-column="59" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README_DEV.md">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="218">
          <caret line="67" column="13" lean-forward="true" selection-start-line="67" selection-start-column="13" selection-end-line="67" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/service/PaymentService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-180">
          <caret line="42" column="81" lean-forward="true" selection-start-line="42" selection-start-column="81" selection-end-line="42" selection-end-column="81" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/paytool/cashier/dao/TransactionDao.java">
      <provider selected="true" editor-type-id="text-editor">
        <state>
          <caret line="35" column="47" lean-forward="true" selection-start-line="35" selection-start-column="47" selection-end-line="35" selection-end-column="47" />
        </state>
      </provider>
    </entry>
  </component>
  <component name="masterDetails">
    <states>
      <state key="ArtifactsStructureConfigurable.UI">
        <settings>
          <artifact-editor />
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="FacetStructureConfigurable.UI">
        <settings>
          <last-edited>No facets are configured</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="GlobalLibrariesConfigurable.UI">
        <settings>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="JdkListConfigurable.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ModuleStructureConfigurable.UI">
        <settings>
          <last-edited>scan-pay-cashier</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
                <option value="0.6" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ProjectJDKs.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ProjectLibrariesConfigurable.UI">
        <settings>
          <last-edited>Maven: ch.qos.logback:logback-classic:1.2.12</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
    </states>
  </component>
</project>