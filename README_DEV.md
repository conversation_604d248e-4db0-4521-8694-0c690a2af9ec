# 扫码支付收银工具开发文档

## 项目概述

这是一个基于Java 1.8开发的PC端扫码支付收银工具，支持被扫支付功能，使用SQLite本地数据库存储交易流水记录。

## 技术栈

- **Java版本**: 1.8
- **构建工具**: Maven 3.x
- **GUI框架**: Java Swing
- **数据库**: SQLite
- **日志框架**: SLF4J + Logback
- **测试框架**: JUnit 4
- **JSON处理**: Jackson

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/paytool/cashier/
│   │       ├── CashierApplication.java          # 主应用程序入口
│   │       ├── dao/                             # 数据访问层
│   │       │   └── TransactionDao.java          # 交易数据访问对象
│   │       ├── gui/                             # 用户界面
│   │       │   ├── MainFrame.java               # 主窗口
│   │       │   └── panel/                       # 界面面板
│   │       │       ├── AmountInputPanel.java    # 金额输入面板
│   │       │       ├── TransactionInfoPanel.java # 交易信息面板
│   │       │       └── StatusOperationPanel.java # 状态操作面板
│   │       ├── model/                           # 数据模型
│   │       │   ├── Transaction.java             # 交易实体
│   │       │   └── TransactionStatus.java       # 交易状态枚举
│   │       └── service/                         # 业务服务层
│   │           ├── DatabaseService.java         # 数据库服务
│   │           ├── OrderService.java            # 订单服务
│   │           ├── PaymentService.java          # 支付服务
│   │           └── ScannerService.java          # 扫码服务
│   └── resources/
│       └── logback.xml                          # 日志配置
└── test/
    └── java/
        └── com/paytool/cashier/
            ├── dao/
            │   └── TransactionDaoTest.java       # DAO测试
            └── service/
                ├── OrderServiceTest.java         # 订单服务测试
                └── PaymentServiceTest.java       # 支付服务测试
```

## 核心功能

### 1. 金额输入区
- 大号字体显示支付金额
- 数字键盘输入支持
- 快捷金额按钮（10元、20元、50元、100元、200元）
- 清空功能

### 2. 交易信息区
- 显示订单号（自动生成）
- 显示交易时间
- 显示支付码信息
- 显示交易状态
- 操作说明

### 3. 状态与操作区
- 交易状态显示（待支付、支付中、支付成功、支付失败）
- 支付码输入框（支持扫码枪和手工输入）
- 扫码按钮
- 清空按钮
- 查询按钮

### 4. 扫码功能
- 支持扫码枪输入（自动识别快速连续输入）
- 支持手工输入支付码
- 支付码格式验证（10-50位字母数字）

### 5. 数据存储
- SQLite本地数据库
- 交易流水记录
- 支持查询历史交易

## 编译和运行

### 环境要求
- JDK 1.8或更高版本
- Maven 3.x

### 编译项目
```bash
mvn clean compile
```

### 运行测试
```bash
mvn test
```

### 打包应用
```bash
mvn clean package
```

### 运行应用
```bash
# 方式1: 使用Maven运行
mvn exec:java -Dexec.mainClass="com.paytool.cashier.CashierApplication"

# 方式2: 运行打包后的JAR
java -jar target/scan-pay-cashier-1.0.0-jar-with-dependencies.jar
```

## 数据库设计

### transactions表结构
```sql
CREATE TABLE transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_no VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(10,2) NOT NULL,
    payment_code VARCHAR(500),
    status VARCHAR(20) NOT NULL,
    create_time DATETIME NOT NULL,
    update_time DATETIME NOT NULL,
    failure_reason VARCHAR(500),
    remark VARCHAR(1000)
);
```

### 索引
- `idx_order_no`: 订单号索引
- `idx_create_time`: 创建时间索引
- `idx_status`: 状态索引

## 订单号规则

订单号格式: `PT + yyyyMMddHHmmss + 6位序列号`

示例: `PT202312251430001234`

- PT: 固定前缀
- 202312251430: 时间戳（年月日时分秒）
- 001234: 6位序列号（自动递增）

## 支付码规则

- 长度: 10-50位
- 字符: 仅支持字母和数字
- 格式: `^[a-zA-Z0-9]+$`

## 扫码枪集成

扫码枪通常模拟键盘输入，系统通过以下方式识别扫码输入：

1. **快速连续输入检测**: 检测按键间隔小于100ms的连续输入
2. **字符过滤**: 只接受字母和数字字符
3. **长度验证**: 输入长度在10-50位之间
4. **超时处理**: 输入停止100ms后自动完成扫码

## 日志配置

日志文件位置: `logs/cashier.log`

日志级别:
- 应用程序: DEBUG
- 根日志: INFO

日志轮转:
- 按天轮转
- 单文件最大10MB
- 保留30天历史

## 测试说明

### 单元测试覆盖
- OrderService: 订单号生成和验证
- PaymentService: 支付处理和交易管理
- TransactionDao: 数据库操作

### 运行所有测试
```bash
mvn test
```

### 运行特定测试
```bash
mvn test -Dtest=OrderServiceTest
mvn test -Dtest=PaymentServiceTest
mvn test -Dtest=TransactionDaoTest
```

## 开发注意事项

1. **Java 1.8兼容性**: 确保所有代码兼容Java 1.8
2. **线程安全**: 服务类使用单例模式，注意线程安全
3. **资源管理**: 数据库连接使用try-with-resources自动关闭
4. **异常处理**: 统一异常处理和日志记录
5. **界面响应**: 长时间操作使用异步处理避免界面卡顿

## 扩展功能建议

1. **支付平台集成**: 集成真实的支付平台API
2. **打印功能**: 添加小票打印功能
3. **报表统计**: 添加交易统计和报表功能
4. **用户管理**: 添加收银员登录和权限管理
5. **配置管理**: 添加系统配置管理界面
6. **数据备份**: 添加数据备份和恢复功能

## 故障排除

### 常见问题

1. **数据库初始化失败**
   - 检查data目录权限
   - 确认SQLite驱动正确加载

2. **扫码枪无法识别**
   - 确认扫码枪配置为键盘模式
   - 检查输入焦点是否在支付码输入框

3. **界面显示异常**
   - 检查系统字体设置
   - 确认屏幕分辨率和DPI设置

4. **日志文件无法创建**
   - 检查logs目录权限
   - 确认磁盘空间充足

## 联系信息

如有问题或建议，请联系开发团队。
