package com.paytool.cashier.gui.panel;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.function.Consumer;

/**
 * 金额输入面板
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class AmountInputPanel extends JPanel {
    
    private static final Logger logger = LoggerFactory.getLogger(AmountInputPanel.class);
    private static final DecimalFormat AMOUNT_FORMAT = new DecimalFormat("#0.00");
    
    private JTextField amountField;
    private Consumer<BigDecimal> amountChangeListener;
    
    public AmountInputPanel() {
        initComponents();
        setupLayout();
        setupEventHandlers();
    }
    
    /**
     * 初始化组件
     */
    private void initComponents() {
        setBorder(new TitledBorder("金额输入区"));
        setPreferredSize(new Dimension(300, 500));
        
        // 金额显示框
        amountField = new JTextField("0.00");
        amountField.setFont(new Font("Arial", Font.BOLD, 24));
        amountField.setHorizontalAlignment(JTextField.RIGHT);
        amountField.setEditable(false);
        amountField.setBackground(Color.WHITE);
        amountField.setBorder(BorderFactory.createLoweredBevelBorder());
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout(10, 10));
        
        // 顶部金额显示
        JPanel amountPanel = new JPanel(new BorderLayout());
        amountPanel.add(new JLabel("支付金额 (元):"), BorderLayout.NORTH);
        amountPanel.add(amountField, BorderLayout.CENTER);
        add(amountPanel, BorderLayout.NORTH);
        
        // 中间数字键盘
        JPanel keypadPanel = createKeypadPanel();
        add(keypadPanel, BorderLayout.CENTER);
        
        // 底部快捷金额按钮
        JPanel quickAmountPanel = createQuickAmountPanel();
        add(quickAmountPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建数字键盘面板
     */
    private JPanel createKeypadPanel() {
        JPanel panel = new JPanel(new GridLayout(4, 3, 5, 5));
        panel.setBorder(BorderFactory.createTitledBorder("数字键盘"));
        
        // 数字按钮 1-9
        for (int i = 1; i <= 9; i++) {
            JButton button = createKeypadButton(String.valueOf(i));
            panel.add(button);
        }
        
        // 底行: 小数点, 0, 退格
        panel.add(createKeypadButton("."));
        panel.add(createKeypadButton("0"));
        
        JButton backspaceButton = createKeypadButton("←");
        backspaceButton.setBackground(Color.ORANGE);
        panel.add(backspaceButton);
        
        return panel;
    }
    
    /**
     * 创建键盘按钮
     */
    private JButton createKeypadButton(String text) {
        JButton button = new JButton(text);
        button.setFont(new Font("Arial", Font.BOLD, 18));
        button.setPreferredSize(new Dimension(60, 50));
        button.addActionListener(new KeypadActionListener(text));
        return button;
    }
    
    /**
     * 创建快捷金额面板
     */
    private JPanel createQuickAmountPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 3, 5, 5));
        panel.setBorder(BorderFactory.createTitledBorder("快捷金额"));
        
        String[] quickAmounts = {"10", "20", "50", "100", "200", "清空"};
        
        for (String amount : quickAmounts) {
            JButton button = new JButton(amount.equals("清空") ? amount : amount + "元");
            button.setFont(new Font("微软雅黑", Font.PLAIN, 12));
            button.addActionListener(e -> {
                if ("清空".equals(amount)) {
                    setAmount(BigDecimal.ZERO);
                } else {
                    setAmount(new BigDecimal(amount));
                }
            });
            panel.add(button);
        }
        
        return panel;
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 金额字段变化监听
        amountField.addPropertyChangeListener("text", evt -> {
            if (amountChangeListener != null) {
                amountChangeListener.accept(getAmount());
            }
        });
    }
    
    /**
     * 获取当前金额
     */
    public BigDecimal getAmount() {
        try {
            String text = amountField.getText();
            if (text == null || text.trim().isEmpty()) {
                return BigDecimal.ZERO;
            }
            return new BigDecimal(text);
        } catch (NumberFormatException e) {
            logger.warn("金额格式错误: {}", amountField.getText());
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 设置金额
     */
    public void setAmount(BigDecimal amount) {
        if (amount == null) {
            amount = BigDecimal.ZERO;
        }
        
        String formattedAmount = AMOUNT_FORMAT.format(amount);
        amountField.setText(formattedAmount);
        
        if (amountChangeListener != null) {
            amountChangeListener.accept(amount);
        }
    }
    
    /**
     * 清空金额
     */
    public void clear() {
        setAmount(BigDecimal.ZERO);
    }
    
    /**
     * 设置金额变化监听器
     */
    public void setAmountChangeListener(Consumer<BigDecimal> listener) {
        this.amountChangeListener = listener;
    }
    
    /**
     * 键盘按钮事件监听器
     */
    private class KeypadActionListener implements ActionListener {
        private final String key;
        
        public KeypadActionListener(String key) {
            this.key = key;
        }
        
        @Override
        public void actionPerformed(ActionEvent e) {
            String currentText = amountField.getText();
            
            switch (key) {
                case "←":
                    // 退格
                    if (currentText.length() > 1) {
                        String newText = currentText.substring(0, currentText.length() - 1);
                        amountField.setText(newText);
                    } else {
                        amountField.setText("0");
                    }
                    break;
                    
                case ".":
                    // 小数点
                    if (!currentText.contains(".")) {
                        amountField.setText(currentText + ".");
                    }
                    break;
                    
                default:
                    // 数字
                    if ("0".equals(currentText) || "0.00".equals(currentText)) {
                        amountField.setText(key);
                    } else {
                        // 限制小数点后最多2位
                        if (currentText.contains(".")) {
                            String[] parts = currentText.split("\\.");
                            if (parts.length > 1 && parts[1].length() >= 2) {
                                return; // 不允许输入更多小数位
                            }
                        }
                        
                        // 限制总长度
                        if (currentText.length() < 10) {
                            amountField.setText(currentText + key);
                        }
                    }
                    break;
            }
            
            // 触发金额变化事件
            if (amountChangeListener != null) {
                amountChangeListener.accept(getAmount());
            }
        }
    }
}
