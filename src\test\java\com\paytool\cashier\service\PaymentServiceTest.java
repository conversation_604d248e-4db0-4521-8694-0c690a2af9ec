package com.paytool.cashier.service;

import com.paytool.cashier.model.Transaction;
import com.paytool.cashier.model.TransactionStatus;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * 支付服务测试类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class PaymentServiceTest {
    
    private PaymentService paymentService;
    
    @Before
    public void setUp() {
        paymentService = PaymentService.getInstance();
    }
    
    @Test
    public void testCreateTransaction() {
        // 测试创建交易
        BigDecimal amount = new BigDecimal("100.50");
        String paymentCode = "1234567890abcdef";
        
        Transaction transaction = paymentService.createTransaction(amount, paymentCode);
        
        assertNotNull(transaction);
        assertNotNull(transaction.getId());
        assertNotNull(transaction.getOrderNo());
        assertEquals(amount, transaction.getAmount());
        assertEquals(paymentCode, transaction.getPaymentCode());
        assertEquals(TransactionStatus.PENDING, transaction.getStatus());
        assertNotNull(transaction.getCreateTime());
        assertNotNull(transaction.getUpdateTime());
        
        System.out.println("创建交易成功: " + transaction);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testCreateTransactionWithInvalidAmount() {
        // 测试无效金额
        paymentService.createTransaction(BigDecimal.ZERO, "1234567890");
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testCreateTransactionWithEmptyPaymentCode() {
        // 测试空支付码
        paymentService.createTransaction(new BigDecimal("100"), "");
    }
    
    @Test
    public void testProcessPayment() throws Exception {
        // 测试支付处理
        BigDecimal amount = new BigDecimal("50.00");
        String paymentCode = "abcdef1234567890";
        
        Transaction transaction = paymentService.createTransaction(amount, paymentCode);
        
        CompletableFuture<Transaction> future = paymentService.processPayment(transaction);
        Transaction result = future.get(5, TimeUnit.SECONDS);
        
        assertNotNull(result);
        assertTrue(result.getStatus().isFinalStatus());
        assertTrue(result.getStatus() == TransactionStatus.SUCCESS || 
                  result.getStatus() == TransactionStatus.FAILED);
        
        if (result.getStatus() == TransactionStatus.FAILED) {
            assertNotNull(result.getFailureReason());
            assertFalse(result.getFailureReason().trim().isEmpty());
        }
        
        System.out.println("支付处理完成: " + result.getStatus().getDisplayName());
        if (result.getFailureReason() != null) {
            System.out.println("失败原因: " + result.getFailureReason());
        }
    }
    
    @Test
    public void testQueryTransaction() {
        // 测试查询交易
        BigDecimal amount = new BigDecimal("75.25");
        String paymentCode = "query1234567890";
        
        Transaction created = paymentService.createTransaction(amount, paymentCode);
        Transaction queried = paymentService.queryTransaction(created.getOrderNo());
        
        assertNotNull(queried);
        assertEquals(created.getId(), queried.getId());
        assertEquals(created.getOrderNo(), queried.getOrderNo());
        assertEquals(created.getAmount(), queried.getAmount());
        assertEquals(created.getPaymentCode(), queried.getPaymentCode());
        
        System.out.println("查询交易成功: " + queried.getOrderNo());
    }
    
    @Test
    public void testQueryNonExistentTransaction() {
        // 测试查询不存在的交易
        Transaction result = paymentService.queryTransaction("PT999999999999999999");
        assertNull(result);
        
        System.out.println("查询不存在的交易返回null");
    }
    
    @Test
    public void testCancelTransaction() {
        // 测试取消交易
        BigDecimal amount = new BigDecimal("30.00");
        String paymentCode = "cancel1234567890";
        
        Transaction transaction = paymentService.createTransaction(amount, paymentCode);
        boolean cancelled = paymentService.cancelTransaction(transaction.getOrderNo());
        
        assertTrue(cancelled);
        
        Transaction updated = paymentService.queryTransaction(transaction.getOrderNo());
        assertEquals(TransactionStatus.CANCELLED, updated.getStatus());
        
        System.out.println("取消交易成功: " + transaction.getOrderNo());
    }
    
    @Test
    public void testCancelNonExistentTransaction() {
        // 测试取消不存在的交易
        boolean result = paymentService.cancelTransaction("PT999999999999999999");
        assertFalse(result);
        
        System.out.println("取消不存在的交易返回false");
    }
    
    @Test
    public void testIsValidPaymentCode() {
        // 测试有效的支付码
        assertTrue(paymentService.isValidPaymentCode("1234567890"));
        assertTrue(paymentService.isValidPaymentCode("abcdef1234567890"));
        assertTrue(paymentService.isValidPaymentCode("ABC123DEF456GHI789"));
        assertTrue(paymentService.isValidPaymentCode("12345678901234567890123456789012345678901234567890"));
        
        // 测试无效的支付码
        assertFalse(paymentService.isValidPaymentCode(null));
        assertFalse(paymentService.isValidPaymentCode(""));
        assertFalse(paymentService.isValidPaymentCode("   "));
        assertFalse(paymentService.isValidPaymentCode("123456789")); // 太短
        assertFalse(paymentService.isValidPaymentCode("123456789012345678901234567890123456789012345678901")); // 太长
        assertFalse(paymentService.isValidPaymentCode("1234567890@#$")); // 包含特殊字符
        assertFalse(paymentService.isValidPaymentCode("1234 567890")); // 包含空格
        
        System.out.println("支付码验证测试通过");
    }
}
