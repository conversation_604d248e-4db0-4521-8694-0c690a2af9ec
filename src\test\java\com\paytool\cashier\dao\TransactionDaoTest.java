package com.paytool.cashier.dao;

import com.paytool.cashier.model.Transaction;
import com.paytool.cashier.model.TransactionStatus;
import com.paytool.cashier.service.DatabaseService;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 交易DAO测试类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class TransactionDaoTest {
    
    private TransactionDao transactionDao;
    
    @Before
    public void setUp() {
        // 初始化数据库
        DatabaseService.getInstance().initDatabase();
        transactionDao = new TransactionDao();
    }
    
    @Test
    public void testSaveAndFindById() {
        // 创建测试交易
        Transaction transaction = createTestTransaction("TEST001", new BigDecimal("100.00"));
        
        // 保存交易
        Long id = transactionDao.save(transaction);
        assertNotNull(id);
        assertTrue(id > 0);
        
        // 根据ID查询
        Transaction found = transactionDao.findById(id);
        assertNotNull(found);
        assertEquals(id, found.getId());
        assertEquals(transaction.getOrderNo(), found.getOrderNo());
        assertEquals(transaction.getAmount(), found.getAmount());
        assertEquals(transaction.getPaymentCode(), found.getPaymentCode());
        assertEquals(transaction.getStatus(), found.getStatus());
        
        System.out.println("保存和查询交易成功: ID=" + id + ", 订单号=" + found.getOrderNo());
    }
    
    @Test
    public void testFindByOrderNo() {
        // 创建测试交易
        Transaction transaction = createTestTransaction("TEST002", new BigDecimal("200.50"));
        
        // 保存交易
        Long id = transactionDao.save(transaction);
        assertNotNull(id);
        
        // 根据订单号查询
        Transaction found = transactionDao.findByOrderNo(transaction.getOrderNo());
        assertNotNull(found);
        assertEquals(transaction.getOrderNo(), found.getOrderNo());
        assertEquals(transaction.getAmount(), found.getAmount());
        
        System.out.println("根据订单号查询成功: " + found.getOrderNo());
    }
    
    @Test
    public void testUpdate() {
        // 创建并保存交易
        Transaction transaction = createTestTransaction("TEST003", new BigDecimal("150.75"));
        Long id = transactionDao.save(transaction);
        assertNotNull(id);
        
        // 更新交易状态
        transaction.setStatus(TransactionStatus.SUCCESS);
        transaction.setRemark("测试更新");
        boolean updated = transactionDao.update(transaction);
        assertTrue(updated);
        
        // 验证更新结果
        Transaction found = transactionDao.findById(id);
        assertEquals(TransactionStatus.SUCCESS, found.getStatus());
        assertEquals("测试更新", found.getRemark());
        
        System.out.println("更新交易成功: " + found.getStatus().getDisplayName());
    }
    
    @Test
    public void testUpdateNonExistent() {
        // 测试更新不存在的交易
        Transaction transaction = createTestTransaction("TEST999", new BigDecimal("999.99"));
        transaction.setId(99999L);
        
        boolean updated = transactionDao.update(transaction);
        assertFalse(updated);
        
        System.out.println("更新不存在的交易返回false");
    }
    
    @Test
    public void testFindRecent() {
        // 创建多个测试交易
        for (int i = 1; i <= 5; i++) {
            Transaction transaction = createTestTransaction("RECENT" + String.format("%03d", i), 
                new BigDecimal(i * 10));
            transactionDao.save(transaction);
            
            // 稍微延迟以确保时间差异
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        // 查询最近的3条记录
        List<Transaction> recent = transactionDao.findRecent(3);
        assertNotNull(recent);
        assertEquals(3, recent.size());
        
        // 验证按时间倒序排列
        for (int i = 0; i < recent.size() - 1; i++) {
            assertTrue(recent.get(i).getCreateTime().isAfter(recent.get(i + 1).getCreateTime()) ||
                      recent.get(i).getCreateTime().isEqual(recent.get(i + 1).getCreateTime()));
        }
        
        System.out.println("查询最近交易成功，数量: " + recent.size());
        for (Transaction t : recent) {
            System.out.println("  - " + t.getOrderNo() + " " + t.getCreateTime());
        }
    }
    
    @Test
    public void testFindByIdNotFound() {
        // 测试查询不存在的ID
        Transaction found = transactionDao.findById(99999L);
        assertNull(found);
        
        System.out.println("查询不存在的ID返回null");
    }
    
    @Test
    public void testFindByOrderNoNotFound() {
        // 测试查询不存在的订单号
        Transaction found = transactionDao.findByOrderNo("NOTEXIST999");
        assertNull(found);
        
        System.out.println("查询不存在的订单号返回null");
    }
    
    @Test
    public void testTransactionWithFailureReason() {
        // 测试带失败原因的交易
        Transaction transaction = createTestTransaction("FAIL001", new BigDecimal("50.00"));
        transaction.setStatus(TransactionStatus.FAILED);
        transaction.setFailureReason("余额不足");
        
        Long id = transactionDao.save(transaction);
        assertNotNull(id);
        
        Transaction found = transactionDao.findById(id);
        assertEquals(TransactionStatus.FAILED, found.getStatus());
        assertEquals("余额不足", found.getFailureReason());
        
        System.out.println("保存失败交易成功: " + found.getFailureReason());
    }
    
    /**
     * 创建测试交易
     */
    private Transaction createTestTransaction(String orderNo, BigDecimal amount) {
        Transaction transaction = new Transaction();
        transaction.setOrderNo(orderNo);
        transaction.setAmount(amount);
        transaction.setPaymentCode("TEST1234567890");
        transaction.setStatus(TransactionStatus.PENDING);
        transaction.setCreateTime(LocalDateTime.now());
        transaction.setUpdateTime(LocalDateTime.now());
        return transaction;
    }
}
