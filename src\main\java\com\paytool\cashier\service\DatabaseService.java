package com.paytool.cashier.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库服务类
 * 负责SQLite数据库的初始化和连接管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class DatabaseService {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseService.class);
    
    private static final String DB_DIR = "data";
    private static final String DB_FILE = "cashier.db";
    private static final String DB_PATH = DB_DIR + File.separator + DB_FILE;
    private static final String DB_URL = "jdbc:sqlite:" + DB_PATH;
    
    private static DatabaseService instance;
    
    private DatabaseService() {
    }
    
    public static synchronized DatabaseService getInstance() {
        if (instance == null) {
            instance = new DatabaseService();
        }
        return instance;
    }
    
    /**
     * 初始化数据库
     */
    public void initDatabase() {
        try {
            // 创建数据目录
            File dataDir = new File(DB_DIR);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
                logger.info("创建数据目录: {}", dataDir.getAbsolutePath());
            }
            
            // 加载SQLite驱动
            Class.forName("org.sqlite.JDBC");
            
            // 创建数据库表
            createTables();
            
            logger.info("数据库初始化完成: {}", DB_PATH);
            
        } catch (Exception e) {
            logger.error("数据库初始化失败", e);
            throw new RuntimeException("数据库初始化失败", e);
        }
    }
    
    /**
     * 获取数据库连接
     */
    public Connection getConnection() throws SQLException {
        return DriverManager.getConnection(DB_URL);
    }
    
    /**
     * 创建数据库表
     */
    private void createTables() throws SQLException {
        String createTransactionTable =
            "CREATE TABLE IF NOT EXISTS transactions (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "order_no VARCHAR(50) NOT NULL UNIQUE," +
                "amount DECIMAL(10,2) NOT NULL," +
                "payment_code VARCHAR(500)," +
                "status VARCHAR(20) NOT NULL," +
                "create_time DATETIME NOT NULL," +
                "update_time DATETIME NOT NULL," +
                "failure_reason VARCHAR(500)," +
                "remark VARCHAR(1000)" +
            ")";

        String createIndexOrderNo =
            "CREATE INDEX IF NOT EXISTS idx_order_no ON transactions(order_no)";

        String createIndexCreateTime =
            "CREATE INDEX IF NOT EXISTS idx_create_time ON transactions(create_time)";

        String createIndexStatus =
            "CREATE INDEX IF NOT EXISTS idx_status ON transactions(status)";
        
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement()) {
            
            stmt.execute(createTransactionTable);
            stmt.execute(createIndexOrderNo);
            stmt.execute(createIndexCreateTime);
            stmt.execute(createIndexStatus);
            
            logger.info("数据库表创建完成");
        }
    }
    
    /**
     * 测试数据库连接
     */
    public boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            logger.error("数据库连接测试失败", e);
            return false;
        }
    }
}
