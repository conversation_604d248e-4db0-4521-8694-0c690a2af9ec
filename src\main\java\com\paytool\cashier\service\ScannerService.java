package com.paytool.cashier.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 扫码服务类
 * 处理扫码枪输入和手工输入支付码
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ScannerService implements KeyListener {
    
    private static final Logger logger = LoggerFactory.getLogger(ScannerService.class);
    
    /** 扫码输入超时时间(毫秒) */
    private static final int SCAN_TIMEOUT_MS = 100;
    
    /** 扫码输入最小长度 */
    private static final int MIN_SCAN_LENGTH = 10;
    
    /** 扫码输入最大长度 */
    private static final int MAX_SCAN_LENGTH = 50;
    
    private static ScannerService instance;
    
    private final ScheduledExecutorService scheduler;
    private final StringBuilder scanBuffer;
    private ScheduledFuture<?> timeoutTask;
    private Consumer<String> scanResultListener;
    private JComponent targetComponent;
    
    private boolean isScanning = false;
    private long lastKeyTime = 0;
    
    private ScannerService() {
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ScannerService");
            t.setDaemon(true);
            return t;
        });
        this.scanBuffer = new StringBuilder();
    }
    
    public static synchronized ScannerService getInstance() {
        if (instance == null) {
            instance = new ScannerService();
        }
        return instance;
    }
    
    /**
     * 注册扫码监听
     */
    public void registerScanListener(JComponent component, Consumer<String> listener) {
        if (targetComponent != null) {
            targetComponent.removeKeyListener(this);
        }
        
        this.targetComponent = component;
        this.scanResultListener = listener;
        
        if (component != null) {
            component.addKeyListener(this);
            component.setFocusable(true);
            logger.info("注册扫码监听器到组件: {}", component.getClass().getSimpleName());
        }
    }
    
    /**
     * 取消扫码监听
     */
    public void unregisterScanListener() {
        if (targetComponent != null) {
            targetComponent.removeKeyListener(this);
            targetComponent = null;
        }
        scanResultListener = null;
        resetScanState();
        logger.info("取消扫码监听器");
    }
    
    /**
     * 手动触发扫码结果
     */
    public void triggerScanResult(String scanData) {
        if (isValidScanData(scanData)) {
            if (scanResultListener != null) {
                SwingUtilities.invokeLater(() -> {
                    scanResultListener.accept(scanData);
                });
            }
            logger.info("手动触发扫码结果: {}", maskScanData(scanData));
        } else {
            logger.warn("无效的扫码数据: {}", maskScanData(scanData));
        }
    }
    
    @Override
    public void keyTyped(KeyEvent e) {
        // 不处理keyTyped事件
    }
    
    @Override
    public void keyPressed(KeyEvent e) {
        long currentTime = System.currentTimeMillis();
        
        // 检查是否是扫码枪输入（快速连续输入）
        if (!isScanning) {
            // 开始新的扫码序列
            if (isStartOfScan(e, currentTime)) {
                startScanning();
            } else {
                return; // 不是扫码输入，忽略
            }
        }
        
        // 处理扫码输入
        if (isScanning) {
            handleScanInput(e, currentTime);
        }
    }
    
    @Override
    public void keyReleased(KeyEvent e) {
        // 不处理keyReleased事件
    }
    
    /**
     * 判断是否是扫码开始
     */
    private boolean isStartOfScan(KeyEvent e, long currentTime) {
        // 如果是字母或数字，且距离上次按键时间较短，可能是扫码开始
        char c = e.getKeyChar();
        return Character.isLetterOrDigit(c) && 
               (lastKeyTime == 0 || currentTime - lastKeyTime < SCAN_TIMEOUT_MS * 2);
    }
    
    /**
     * 开始扫码
     */
    private void startScanning() {
        isScanning = true;
        scanBuffer.setLength(0);
        
        // 取消之前的超时任务
        if (timeoutTask != null) {
            timeoutTask.cancel(false);
        }
        
        logger.debug("开始扫码输入");
    }
    
    /**
     * 处理扫码输入
     */
    private void handleScanInput(KeyEvent e, long currentTime) {
        lastKeyTime = currentTime;
        
        // 检查输入间隔，如果太长则认为不是扫码
        if (scanBuffer.length() > 0 && currentTime - lastKeyTime > SCAN_TIMEOUT_MS) {
            resetScanState();
            return;
        }
        
        // 处理按键
        if (e.getKeyCode() == KeyEvent.VK_ENTER) {
            // 回车键，结束扫码
            finishScanning();
        } else {
            char c = e.getKeyChar();
            if (Character.isLetterOrDigit(c)) {
                scanBuffer.append(c);
                
                // 检查长度限制
                if (scanBuffer.length() > MAX_SCAN_LENGTH) {
                    logger.warn("扫码数据过长，重置扫码状态");
                    resetScanState();
                    return;
                }
                
                // 设置超时任务
                scheduleTimeout();
            } else {
                // 非字母数字字符，可能不是扫码输入
                logger.debug("扫码输入包含非字母数字字符: {}", (int)c);
            }
        }
    }
    
    /**
     * 设置超时任务
     */
    private void scheduleTimeout() {
        if (timeoutTask != null) {
            timeoutTask.cancel(false);
        }
        
        timeoutTask = scheduler.schedule(() -> {
            if (isScanning) {
                finishScanning();
            }
        }, SCAN_TIMEOUT_MS, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 完成扫码
     */
    private void finishScanning() {
        String scanData = scanBuffer.toString();
        resetScanState();
        
        if (isValidScanData(scanData)) {
            logger.info("扫码完成: {}", maskScanData(scanData));
            
            if (scanResultListener != null) {
                SwingUtilities.invokeLater(() -> {
                    scanResultListener.accept(scanData);
                });
            }
        } else {
            logger.debug("扫码数据无效: {}", maskScanData(scanData));
        }
    }
    
    /**
     * 重置扫码状态
     */
    private void resetScanState() {
        isScanning = false;
        scanBuffer.setLength(0);
        
        if (timeoutTask != null) {
            timeoutTask.cancel(false);
            timeoutTask = null;
        }
    }
    
    /**
     * 验证扫码数据
     */
    private boolean isValidScanData(String scanData) {
        if (scanData == null || scanData.trim().isEmpty()) {
            return false;
        }
        
        String data = scanData.trim();
        
        // 检查长度
        if (data.length() < MIN_SCAN_LENGTH || data.length() > MAX_SCAN_LENGTH) {
            return false;
        }
        
        // 检查字符
        return data.matches("^[a-zA-Z0-9]+$");
    }
    
    /**
     * 掩码显示扫码数据（用于日志）
     */
    private String maskScanData(String scanData) {
        if (scanData == null || scanData.length() <= 6) {
            return "***";
        }
        
        return scanData.substring(0, 3) + "***" + scanData.substring(scanData.length() - 3);
    }
    
    /**
     * 关闭服务
     */
    public void shutdown() {
        unregisterScanListener();
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(1, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("扫码服务已关闭");
    }
}
