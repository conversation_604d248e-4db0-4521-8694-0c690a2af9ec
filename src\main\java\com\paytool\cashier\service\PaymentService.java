package com.paytool.cashier.service;

import com.paytool.cashier.dao.TransactionDao;
import com.paytool.cashier.model.Transaction;
import com.paytool.cashier.model.TransactionStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 支付服务类
 * 负责处理支付逻辑和交易状态管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class PaymentService {
    
    private static final Logger logger = LoggerFactory.getLogger(PaymentService.class);
    
    private static PaymentService instance;
    private final TransactionDao transactionDao;
    private final OrderService orderService;
    
    private PaymentService() {
        this.transactionDao = new TransactionDao();
        this.orderService = OrderService.getInstance();
    }
    
    public static synchronized PaymentService getInstance() {
        if (instance == null) {
            instance = new PaymentService();
        }
        return instance;
    }
    
    /**
     * 创建新的交易
     */
    public Transaction createTransaction(BigDecimal amount, String paymentCode) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("支付金额必须大于0");
        }
        
        if (paymentCode == null || paymentCode.trim().isEmpty()) {
            throw new IllegalArgumentException("支付码不能为空");
        }
        
        String orderNo = orderService.generateOrderNo();
        Transaction transaction = new Transaction(orderNo, amount, paymentCode.trim());
        
        Long id = transactionDao.save(transaction);
        if (id != null) {
            logger.info("创建交易成功: 订单号={}, 金额={}, 支付码={}", orderNo, amount, paymentCode);
            return transaction;
        } else {
            throw new RuntimeException("创建交易失败");
        }
    }
    
    /**
     * 处理支付
     */
    public CompletableFuture<Transaction> processPayment(Transaction transaction) {
        logger.info("开始处理支付: 订单号={}", transaction.getOrderNo());
        
        // 更新状态为支付中
        transaction.setStatus(TransactionStatus.PROCESSING);
        transactionDao.update(transaction);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 模拟支付处理时间 (1-3秒)
                int delay = ThreadLocalRandom.current().nextInt(1000, 3000);
                Thread.sleep(delay);
                
                // 模拟支付结果 (90%成功率)
                boolean success = ThreadLocalRandom.current().nextDouble() < 0.9;
                
                if (success) {
                    transaction.setStatus(TransactionStatus.SUCCESS);
                    logger.info("支付成功: 订单号={}", transaction.getOrderNo());
                } else {
                    transaction.setStatus(TransactionStatus.FAILED);
                    transaction.setFailureReason("模拟支付失败 - " + getRandomFailureReason());
                    logger.warn("支付失败: 订单号={}, 原因={}", 
                        transaction.getOrderNo(), transaction.getFailureReason());
                }
                
                transactionDao.update(transaction);
                return transaction;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                transaction.setStatus(TransactionStatus.FAILED);
                transaction.setFailureReason("支付处理被中断");
                transactionDao.update(transaction);
                logger.error("支付处理被中断: 订单号={}", transaction.getOrderNo());
                return transaction;
            } catch (Exception e) {
                transaction.setStatus(TransactionStatus.FAILED);
                transaction.setFailureReason("系统异常: " + e.getMessage());
                transactionDao.update(transaction);
                logger.error("支付处理异常: 订单号={}", transaction.getOrderNo(), e);
                return transaction;
            }
        });
    }
    
    /**
     * 取消交易
     */
    public boolean cancelTransaction(String orderNo) {
        Transaction transaction = transactionDao.findByOrderNo(orderNo);
        if (transaction == null) {
            logger.warn("取消交易失败，交易不存在: 订单号={}", orderNo);
            return false;
        }
        
        if (transaction.getStatus().isFinalStatus()) {
            logger.warn("取消交易失败，交易已完成: 订单号={}, 状态={}", 
                orderNo, transaction.getStatus().getDisplayName());
            return false;
        }
        
        transaction.setStatus(TransactionStatus.CANCELLED);
        boolean success = transactionDao.update(transaction);
        
        if (success) {
            logger.info("取消交易成功: 订单号={}", orderNo);
        } else {
            logger.error("取消交易失败: 订单号={}", orderNo);
        }
        
        return success;
    }
    
    /**
     * 查询交易
     */
    public Transaction queryTransaction(String orderNo) {
        return transactionDao.findByOrderNo(orderNo);
    }
    
    /**
     * 验证支付码格式
     */
    public boolean isValidPaymentCode(String paymentCode) {
        if (paymentCode == null || paymentCode.trim().isEmpty()) {
            return false;
        }
        
        String code = paymentCode.trim();
        
        // 支付码长度应在10-50位之间
        if (code.length() < 10 || code.length() > 50) {
            return false;
        }
        
        // 支付码应该只包含数字和字母
        return code.matches("^[a-zA-Z0-9]+$");
    }
    
    /**
     * 获取随机失败原因
     */
    private String getRandomFailureReason() {
        String[] reasons = {
            "余额不足",
            "网络超时",
            "银行卡异常",
            "支付密码错误",
            "账户被冻结",
            "交易限额超限"
        };
        
        int index = ThreadLocalRandom.current().nextInt(reasons.length);
        return reasons[index];
    }
}
