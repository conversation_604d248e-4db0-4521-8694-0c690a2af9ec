package com.paytool.cashier.dao;

import com.paytool.cashier.model.Transaction;
import com.paytool.cashier.model.TransactionStatus;
import com.paytool.cashier.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 交易记录数据访问对象
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class TransactionDao {
    
    private static final Logger logger = LoggerFactory.getLogger(TransactionDao.class);
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final DatabaseService databaseService;
    
    public TransactionDao() {
        this.databaseService = DatabaseService.getInstance();
    }
    
    /**
     * 保存交易记录
     */
    public Long save(Transaction transaction) {
        String sql =
            "INSERT INTO transactions (order_no, amount, payment_code, status, " +
                                    "create_time, update_time, failure_reason, remark) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = databaseService.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setString(1, transaction.getOrderNo());
            stmt.setBigDecimal(2, transaction.getAmount());
            stmt.setString(3, transaction.getPaymentCode());
            stmt.setString(4, transaction.getStatus().name());
            stmt.setString(5, transaction.getCreateTime().format(DATETIME_FORMATTER));
            stmt.setString(6, transaction.getUpdateTime().format(DATETIME_FORMATTER));
            stmt.setString(7, transaction.getFailureReason());
            stmt.setString(8, transaction.getRemark());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows > 0) {
                try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        Long id = generatedKeys.getLong(1);
                        transaction.setId(id);
                        logger.info("保存交易记录成功, ID: {}, 订单号: {}", id, transaction.getOrderNo());
                        return id;
                    }
                }
            }
            
        } catch (SQLException e) {
            logger.error("保存交易记录失败: {}", transaction.getOrderNo(), e);
            throw new RuntimeException("保存交易记录失败", e);
        }
        
        return null;
    }
    
    /**
     * 更新交易记录
     */
    public boolean update(Transaction transaction) {
        String sql =
            "UPDATE transactions " +
            "SET amount = ?, payment_code = ?, status = ?, update_time = ?, " +
                "failure_reason = ?, remark = ? " +
            "WHERE id = ?";
        
        try (Connection conn = databaseService.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setBigDecimal(1, transaction.getAmount());
            stmt.setString(2, transaction.getPaymentCode());
            stmt.setString(3, transaction.getStatus().name());
            stmt.setString(4, transaction.getUpdateTime().format(DATETIME_FORMATTER));
            stmt.setString(5, transaction.getFailureReason());
            stmt.setString(6, transaction.getRemark());
            stmt.setLong(7, transaction.getId());
            
            int affectedRows = stmt.executeUpdate();
            boolean success = affectedRows > 0;
            
            if (success) {
                logger.info("更新交易记录成功, ID: {}, 订单号: {}", transaction.getId(), transaction.getOrderNo());
            } else {
                logger.warn("更新交易记录失败, 记录不存在, ID: {}", transaction.getId());
            }
            
            return success;
            
        } catch (SQLException e) {
            logger.error("更新交易记录失败: {}", transaction.getId(), e);
            throw new RuntimeException("更新交易记录失败", e);
        }
    }
    
    /**
     * 根据ID查询交易记录
     */
    public Transaction findById(Long id) {
        String sql = "SELECT * FROM transactions WHERE id = ?";
        
        try (Connection conn = databaseService.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, id);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToTransaction(rs);
                }
            }
            
        } catch (SQLException e) {
            logger.error("查询交易记录失败, ID: {}", id, e);
            throw new RuntimeException("查询交易记录失败", e);
        }
        
        return null;
    }
    
    /**
     * 根据订单号查询交易记录
     */
    public Transaction findByOrderNo(String orderNo) {
        String sql = "SELECT * FROM transactions WHERE order_no = ?";
        
        try (Connection conn = databaseService.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, orderNo);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToTransaction(rs);
                }
            }
            
        } catch (SQLException e) {
            logger.error("查询交易记录失败, 订单号: {}", orderNo, e);
            throw new RuntimeException("查询交易记录失败", e);
        }
        
        return null;
    }
    
    /**
     * 查询最近的交易记录
     */
    public List<Transaction> findRecent(int limit) {
        String sql = "SELECT * FROM transactions ORDER BY create_time DESC LIMIT ?";
        
        List<Transaction> transactions = new ArrayList<>();
        
        try (Connection conn = databaseService.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, limit);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    transactions.add(mapResultSetToTransaction(rs));
                }
            }
            
        } catch (SQLException e) {
            logger.error("查询最近交易记录失败", e);
            throw new RuntimeException("查询最近交易记录失败", e);
        }
        
        return transactions;
    }
    
    /**
     * 将ResultSet映射为Transaction对象
     */
    private Transaction mapResultSetToTransaction(ResultSet rs) throws SQLException {
        Transaction transaction = new Transaction();
        transaction.setId(rs.getLong("id"));
        transaction.setOrderNo(rs.getString("order_no"));
        transaction.setAmount(rs.getBigDecimal("amount"));
        transaction.setPaymentCode(rs.getString("payment_code"));
        transaction.setStatus(TransactionStatus.valueOf(rs.getString("status")));
        transaction.setCreateTime(LocalDateTime.parse(rs.getString("create_time"), DATETIME_FORMATTER));
        transaction.setUpdateTime(LocalDateTime.parse(rs.getString("update_time"), DATETIME_FORMATTER));
        transaction.setFailureReason(rs.getString("failure_reason"));
        transaction.setRemark(rs.getString("remark"));
        return transaction;
    }
}
