package com.paytool.cashier.gui.panel;

import com.paytool.cashier.model.TransactionStatus;
import com.paytool.cashier.service.ScannerService;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.util.function.Consumer;

/**
 * 状态与操作面板
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class StatusOperationPanel extends JPanel {
    
    private JLabel statusDisplayLabel;
    private JTextField paymentCodeField;
    private JButton scanButton;
    private JButton clearButton;
    private JButton queryButton;
    
    private Runnable scanListener;
    private Runnable clearListener;
    private Runnable queryListener;
    private Consumer<String> paymentCodeListener;

    private ScannerService scannerService;

    public StatusOperationPanel() {
        this.scannerService = ScannerService.getInstance();
        initComponents();
        setupLayout();
        setupEventHandlers();
        setupScannerService();
    }
    
    /**
     * 初始化组件
     */
    private void initComponents() {
        setBorder(new TitledBorder("状态与操作区"));
        setPreferredSize(new Dimension(300, 500));
        
        // 状态显示
        statusDisplayLabel = new JLabel("待支付", JLabel.CENTER);
        statusDisplayLabel.setFont(new Font("微软雅黑", Font.BOLD, 20));
        statusDisplayLabel.setOpaque(true);
        statusDisplayLabel.setBackground(Color.LIGHT_GRAY);
        statusDisplayLabel.setBorder(BorderFactory.createRaisedBevelBorder());
        statusDisplayLabel.setPreferredSize(new Dimension(250, 60));
        
        // 支付码输入框
        paymentCodeField = new JTextField();
        paymentCodeField.setFont(new Font("Courier New", Font.PLAIN, 14));
        paymentCodeField.setBorder(BorderFactory.createTitledBorder("支付码"));
        paymentCodeField.setToolTipText("请扫描或手工输入支付码");
        
        // 操作按钮
        scanButton = new JButton("扫码");
        scanButton.setFont(new Font("微软雅黑", Font.BOLD, 16));
        scanButton.setPreferredSize(new Dimension(100, 40));
        scanButton.setBackground(new Color(0, 150, 0));
        scanButton.setForeground(Color.WHITE);
        
        clearButton = new JButton("清空");
        clearButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        clearButton.setPreferredSize(new Dimension(100, 40));
        clearButton.setBackground(Color.ORANGE);
        clearButton.setForeground(Color.WHITE);
        
        queryButton = new JButton("查询");
        queryButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        queryButton.setPreferredSize(new Dimension(100, 40));
        queryButton.setBackground(Color.BLUE);
        queryButton.setForeground(Color.WHITE);
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout(10, 10));
        
        // 顶部状态显示
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(BorderFactory.createTitledBorder("交易状态"));
        statusPanel.add(statusDisplayLabel, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.NORTH);
        
        // 中间支付码输入区
        JPanel inputPanel = new JPanel(new BorderLayout(5, 5));
        inputPanel.setBorder(BorderFactory.createTitledBorder("支付码输入"));
        
        inputPanel.add(paymentCodeField, BorderLayout.CENTER);
        
        // 扫码按钮
        JPanel scanPanel = new JPanel(new FlowLayout());
        scanPanel.add(scanButton);
        inputPanel.add(scanPanel, BorderLayout.SOUTH);
        
        add(inputPanel, BorderLayout.CENTER);
        
        // 底部操作按钮
        JPanel buttonPanel = new JPanel(new GridLayout(3, 1, 5, 5));
        buttonPanel.setBorder(BorderFactory.createTitledBorder("操作"));
        
        buttonPanel.add(clearButton);
        buttonPanel.add(queryButton);
        
        // 添加一个空白按钮占位
        JButton placeholderButton = new JButton("设置");
        placeholderButton.setEnabled(false);
        buttonPanel.add(placeholderButton);
        
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 扫码按钮事件
        scanButton.addActionListener(e -> {
            if (scanListener != null) {
                scanListener.run();
            }
        });
        
        // 清空按钮事件
        clearButton.addActionListener(e -> {
            if (clearListener != null) {
                clearListener.run();
            }
        });
        
        // 查询按钮事件
        queryButton.addActionListener(e -> {
            if (queryListener != null) {
                queryListener.run();
            }
        });
        
        // 支付码输入框事件
        paymentCodeField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    String paymentCode = paymentCodeField.getText().trim();
                    if (!paymentCode.isEmpty() && paymentCodeListener != null) {
                        paymentCodeListener.accept(paymentCode);
                    }
                }
            }
        });
        
        // 支付码输入框失去焦点事件
        paymentCodeField.addActionListener(e -> {
            String paymentCode = paymentCodeField.getText().trim();
            if (!paymentCode.isEmpty() && paymentCodeListener != null) {
                paymentCodeListener.accept(paymentCode);
            }
        });
    }
    
    /**
     * 更新状态显示
     */
    public void updateStatus(TransactionStatus status) {
        if (status == null) {
            statusDisplayLabel.setText("--");
            statusDisplayLabel.setBackground(Color.LIGHT_GRAY);
            return;
        }
        
        statusDisplayLabel.setText(status.getDisplayName());
        
        // 根据状态设置背景色
        switch (status) {
            case PENDING:
                statusDisplayLabel.setBackground(Color.LIGHT_GRAY);
                statusDisplayLabel.setForeground(Color.BLACK);
                break;
            case PROCESSING:
                statusDisplayLabel.setBackground(Color.ORANGE);
                statusDisplayLabel.setForeground(Color.WHITE);
                break;
            case SUCCESS:
                statusDisplayLabel.setBackground(Color.GREEN);
                statusDisplayLabel.setForeground(Color.WHITE);
                break;
            case FAILED:
                statusDisplayLabel.setBackground(Color.RED);
                statusDisplayLabel.setForeground(Color.WHITE);
                break;
            case CANCELLED:
                statusDisplayLabel.setBackground(Color.GRAY);
                statusDisplayLabel.setForeground(Color.WHITE);
                break;
            default:
                statusDisplayLabel.setBackground(Color.LIGHT_GRAY);
                statusDisplayLabel.setForeground(Color.BLACK);
                break;
        }
        
        // 根据状态启用/禁用按钮
        boolean isProcessing = status == TransactionStatus.PROCESSING;
        scanButton.setEnabled(!isProcessing);
        paymentCodeField.setEnabled(!isProcessing);
    }
    
    /**
     * 聚焦到支付码输入框
     */
    public void focusPaymentCodeInput() {
        paymentCodeField.requestFocus();
    }
    
    /**
     * 获取支付码
     */
    public String getPaymentCode() {
        return paymentCodeField.getText().trim();
    }
    
    /**
     * 设置支付码
     */
    public void setPaymentCode(String paymentCode) {
        paymentCodeField.setText(paymentCode == null ? "" : paymentCode);
    }
    
    /**
     * 清空支付码
     */
    public void clearPaymentCode() {
        paymentCodeField.setText("");
    }
    
    /**
     * 清空所有内容
     */
    public void clear() {
        clearPaymentCode();
        updateStatus(TransactionStatus.PENDING);
    }
    
    // 设置监听器方法
    public void setScanListener(Runnable listener) {
        this.scanListener = listener;
    }
    
    public void setClearListener(Runnable listener) {
        this.clearListener = listener;
    }
    
    public void setQueryListener(Runnable listener) {
        this.queryListener = listener;
    }
    
    public void setPaymentCodeListener(Consumer<String> listener) {
        this.paymentCodeListener = listener;
    }

    /**
     * 设置扫码服务
     */
    private void setupScannerService() {
        // 注册扫码监听器到支付码输入框
        scannerService.registerScanListener(paymentCodeField, this::onScanResult);
    }

    /**
     * 扫码结果处理
     */
    private void onScanResult(String scanData) {
        // 设置支付码到输入框
        setPaymentCode(scanData);

        // 触发支付码输入事件
        if (paymentCodeListener != null) {
            paymentCodeListener.accept(scanData);
        }
    }

    /**
     * 清理资源
     */
    public void dispose() {
        if (scannerService != null) {
            scannerService.unregisterScanListener();
        }
    }
}
