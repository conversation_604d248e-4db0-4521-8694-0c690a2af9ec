package com.paytool.cashier.gui.panel;

import com.paytool.cashier.model.Transaction;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.time.format.DateTimeFormatter;

/**
 * 交易信息面板
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class TransactionInfoPanel extends JPanel {
    
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private JLabel orderNoLabel;
    private JLabel amountLabel;
    private JLabel timeLabel;
    private JTextArea paymentCodeArea;
    private JLabel statusLabel;
    
    public TransactionInfoPanel() {
        initComponents();
        setupLayout();
    }
    
    /**
     * 初始化组件
     */
    private void initComponents() {
        setBorder(new TitledBorder("交易信息区"));
        setPreferredSize(new Dimension(350, 500));
        
        // 订单号
        orderNoLabel = new JLabel("--");
        orderNoLabel.setFont(new Font("微软雅黑", Font.BOLD, 14));
        
        // 金额
        amountLabel = new JLabel("--");
        amountLabel.setFont(new Font("Arial", Font.BOLD, 16));
        amountLabel.setForeground(Color.BLUE);
        
        // 时间
        timeLabel = new JLabel("--");
        timeLabel.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        
        // 支付码信息
        paymentCodeArea = new JTextArea(3, 20);
        paymentCodeArea.setFont(new Font("Courier New", Font.PLAIN, 12));
        paymentCodeArea.setEditable(false);
        paymentCodeArea.setBackground(getBackground());
        paymentCodeArea.setBorder(BorderFactory.createLoweredBevelBorder());
        paymentCodeArea.setLineWrap(true);
        paymentCodeArea.setWrapStyleWord(true);
        
        // 状态
        statusLabel = new JLabel("--");
        statusLabel.setFont(new Font("微软雅黑", Font.BOLD, 14));
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout(10, 10));
        
        // 主信息面板
        JPanel mainInfoPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // 订单号
        gbc.gridx = 0; gbc.gridy = 0;
        mainInfoPanel.add(new JLabel("订单号:"), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        mainInfoPanel.add(orderNoLabel, gbc);
        
        // 交易金额
        gbc.gridx = 0; gbc.gridy = 1;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        mainInfoPanel.add(new JLabel("交易金额:"), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        mainInfoPanel.add(amountLabel, gbc);
        
        // 交易时间
        gbc.gridx = 0; gbc.gridy = 2;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        mainInfoPanel.add(new JLabel("交易时间:"), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        mainInfoPanel.add(timeLabel, gbc);
        
        // 交易状态
        gbc.gridx = 0; gbc.gridy = 3;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        mainInfoPanel.add(new JLabel("交易状态:"), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        mainInfoPanel.add(statusLabel, gbc);
        
        add(mainInfoPanel, BorderLayout.NORTH);
        
        // 支付码信息面板
        JPanel paymentCodePanel = new JPanel(new BorderLayout());
        paymentCodePanel.setBorder(BorderFactory.createTitledBorder("支付码信息"));
        paymentCodePanel.add(new JScrollPane(paymentCodeArea), BorderLayout.CENTER);
        
        add(paymentCodePanel, BorderLayout.CENTER);
        
        // 底部说明面板
        JPanel descPanel = new JPanel(new BorderLayout());
        descPanel.setBorder(BorderFactory.createTitledBorder("操作说明"));
        
        JTextArea descArea = new JTextArea();
        descArea.setText(
            "操作流程:\n" +
            "1. 在左侧输入支付金额\n" +
            "2. 点击右侧\"扫码\"按钮\n" +
            "3. 扫描或输入支付码\n" +
            "4. 等待支付结果\n" +
            "\n" +
            "注意事项:\n" +
            "* 支付码长度10-50位\n" +
            "* 仅支持数字和字母\n" +
            "* 支付过程中请勿重复操作"
        );
        descArea.setFont(new Font("微软雅黑", Font.PLAIN, 11));
        descArea.setEditable(false);
        descArea.setBackground(getBackground());
        descArea.setLineWrap(true);
        descArea.setWrapStyleWord(true);
        
        descPanel.add(new JScrollPane(descArea), BorderLayout.CENTER);
        add(descPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 更新交易信息
     */
    public void updateTransaction(Transaction transaction) {
        if (transaction == null) {
            clear();
            return;
        }
        
        // 更新订单号
        orderNoLabel.setText(transaction.getOrderNo());
        
        // 更新金额
        amountLabel.setText("¥ " + transaction.getAmount().toString());
        
        // 更新时间
        if (transaction.getCreateTime() != null) {
            timeLabel.setText(transaction.getCreateTime().format(TIME_FORMATTER));
        }
        
        // 更新支付码
        String paymentCode = transaction.getPaymentCode();
        if (paymentCode != null && !paymentCode.trim().isEmpty()) {
            // 格式化显示支付码，每10个字符换行
            StringBuilder formatted = new StringBuilder();
            for (int i = 0; i < paymentCode.length(); i += 10) {
                if (i > 0) formatted.append("\n");
                int end = Math.min(i + 10, paymentCode.length());
                formatted.append(paymentCode.substring(i, end));
            }
            paymentCodeArea.setText(formatted.toString());
        } else {
            paymentCodeArea.setText("未输入");
        }
        
        // 更新状态
        updateStatus(transaction);
    }
    
    /**
     * 更新状态显示
     */
    private void updateStatus(Transaction transaction) {
        if (transaction == null) {
            statusLabel.setText("--");
            statusLabel.setForeground(Color.BLACK);
            return;
        }
        
        String statusText = transaction.getStatus().getDisplayName();
        
        // 如果有失败原因，显示详细信息
        if (transaction.getStatus().isFailure() && 
            transaction.getFailureReason() != null && 
            !transaction.getFailureReason().trim().isEmpty()) {
            statusText += " (" + transaction.getFailureReason() + ")";
        }
        
        statusLabel.setText(statusText);
        
        // 根据状态设置颜色
        switch (transaction.getStatus()) {
            case PENDING:
                statusLabel.setForeground(Color.GRAY);
                break;
            case PROCESSING:
                statusLabel.setForeground(Color.ORANGE);
                break;
            case SUCCESS:
                statusLabel.setForeground(Color.GREEN);
                break;
            case FAILED:
                statusLabel.setForeground(Color.RED);
                break;
            case CANCELLED:
                statusLabel.setForeground(Color.GRAY);
                break;
            default:
                statusLabel.setForeground(Color.BLACK);
                break;
        }
    }
    
    /**
     * 清空显示
     */
    public void clear() {
        orderNoLabel.setText("--");
        amountLabel.setText("--");
        timeLabel.setText("--");
        paymentCodeArea.setText("");
        statusLabel.setText("--");
        statusLabel.setForeground(Color.BLACK);
    }
}
